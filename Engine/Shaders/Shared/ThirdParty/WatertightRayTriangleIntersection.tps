<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Watertight Ray Triangle Intersection</Name>
  <Location>/Engine/Shaders/Shared/ThirdParty/RayTriangleIntersection.h</Location>
  <Function>This is an advanced ray-triangle intersection algorithm that is needed for ray tracing implementation in UE. The source code was modified to Unreal coding standards.</Function>
  <Eula>https://creativecommons.org/licenses/by-nd/3.0/</Eula>
  <RedistributeTo>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>