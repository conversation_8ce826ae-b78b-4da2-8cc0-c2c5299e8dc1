<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Intel Extensions Framework</Name>
  <Location>//UE4/Dev-Rendering/Engine/Source/ThirdParty/Intel/ExtensionsFramework</Location>
  <Function>Allows for enhanced rendering functionality on Intel GPUs.</Function>
  <Eula>Included in the header file.</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
	<EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>//UE4/Main/Engine/Source/ThirdParty/Licenses/IntelExtensionsFramework.txt</LicenseFolder>
</TpsData>