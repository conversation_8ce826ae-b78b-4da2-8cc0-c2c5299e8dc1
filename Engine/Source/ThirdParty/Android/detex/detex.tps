<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>detex v0.1.2</Name>
  <Location></Location>
  <Date>2015-03-20T12:18:17.2560084-04:00</Date>
  <Function>Decodes alpha ETC2 into RGBA texture format on Android devices without support. Allows us to distribute smaller packages with alpha textures since ETC1 doesn't support alpha compression. Compiled into the engine for Android games as part of the OpenGL RHI.</Function>
  <Eula>https://github.com/hglm/detex/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>