diff --git a/hwcpipe/include/hwcpipe/detail/internal_types.hpp b/hwcpipe/include/hwcpipe/detail/internal_types.hpp
index d1aaf90..6c7f65d 100644
--- a/hwcpipe/include/hwcpipe/detail/internal_types.hpp
+++ b/hwcpipe/include/hwcpipe/detail/internal_types.hpp
@@ -17,6 +17,7 @@
 
 #include <cstdint>
 #include <initializer_list>
+#include <vector>
 #include <system_error>
 namespace hwcpipe {
 namespace detail {
@@ -72,7 +73,7 @@ struct expression_definition {
      * be implicitly registered with the sampler so that they can be collected
      * when it is polled.
      */
-    const std::initializer_list<hwcpipe_counter> dependencies;
+    const std::vector<hwcpipe_counter> dependencies;
 };
 
 } // namespace expression
@@ -96,7 +97,8 @@ struct block_offset {
  */
 struct counter_definition {
     enum class type { invalid, hardware, expression };
-    union u {
+    type tag;
+    struct u {
         block_offset address{};
         expression::expression_definition expression;
         explicit u(expression::expression_definition expression)
@@ -104,7 +106,6 @@ struct counter_definition {
         explicit u(block_offset address)
             : address(address) {}
     } u;
-    type tag;
 
     counter_definition()
         : tag(type::invalid)
