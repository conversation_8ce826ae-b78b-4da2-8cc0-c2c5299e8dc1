<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>ANGLE</Name>
  <Location>/Engine/Source/ThirdParty/ANGLE/</Location>
  <Date>2016-06-07T15:43:08.6211921-04:00</Date>
  <Function>Replicates the graphics feature set provided by many mobile devices ( including HTML ) on windows.</Function>
  <Justification>Allows faster iterations while developing for HTML5 platform and possibly later for other mobile allows faster iterations while developing for HTML5 platform and possibly later for other mobile  allows faster iterations while developing for HTML5 platform and possibly later for other mobile platforms</Justification>
  <Eula>http://opensource.org/licenses/BSD-3-Clause</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/Angle_License.txt</LicenseFolder>
</TpsData>