<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup useLegacyV2RuntimeActivationPolicy="true">

        <!--
          Use supportedRuntime tags to explicitly specify the version(s) of the .NET Framework runtime that
          the custom action should run on. If no versions are specified, the chosen version of the runtime
          will be the "best" match to what Microsoft.Deployment.WindowsInstaller.dll was built against.

          WARNING: leaving the version unspecified is dangerous as it introduces a risk of compatibility
          problems with future versions of the .NET Framework runtime. It is highly recommended that you specify
          only the version(s) of the .NET Framework runtime that you have tested against.

          Note for .NET Framework v3.0 and v3.5, the runtime version is still v2.0.

          In order to enable .NET Framework version 2.0 runtime activation policy, which is to load all assemblies
          by using the latest supported runtime, @useLegacyV2RuntimeActivationPolicy="true".

          For more information, see http://msdn.microsoft.com/en-us/library/bbx34a2h.aspx
        -->

        <supportedRuntime version="v4.0" />
        <supportedRuntime version="v2.0.50727"/>

    </startup>

    <!--
      Add additional configuration settings here. For more information on application config files,
      see http://msdn.microsoft.com/en-us/library/kza1yk3a.aspx
    -->

</configuration>
