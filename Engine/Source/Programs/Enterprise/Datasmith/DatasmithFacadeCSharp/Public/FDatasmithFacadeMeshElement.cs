// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeMeshElement : FDatasmithFacadeElement {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeMeshElement(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeMeshElement obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeMeshElement(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeMeshElement(string InElementName) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeMeshElement(InElementName), true) {
  }

  public string GetFile() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetFile(swigCPtr));
    return ret;
  }

  public void SetFile(string InFile) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetFile(swigCPtr, InFile);
  }

  private void InternalGetFileHash(System.Text.StringBuilder OutBuffer, ulong BufferSize) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_InternalGetFileHash(swigCPtr, OutBuffer, BufferSize);
  }

  public void SetFileHash(string Hash) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetFileHash(swigCPtr, Hash);
  }

  public void SetDimensions(float InArea, float InWidth, float InHeight, float InDepth) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetDimensions(swigCPtr, InArea, InWidth, InHeight, InDepth);
  }

  public float GetArea() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetArea(swigCPtr);
    return ret;
  }

  public float GetWidth() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetWidth(swigCPtr);
    return ret;
  }

  public float GetHeight() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetHeight(swigCPtr);
    return ret;
  }

  public float GetDepth() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetDepth(swigCPtr);
    return ret;
  }

  public int GetLightmapCoordinateIndex() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetLightmapCoordinateIndex(swigCPtr);
    return ret;
  }

  public void SetLightmapCoordinateIndex(int UVChannel) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetLightmapCoordinateIndex(swigCPtr, UVChannel);
  }

  public int GetLightmapSourceUV() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetLightmapSourceUV(swigCPtr);
    return ret;
  }

  public void SetLightmapSourceUV(int UVChannel) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetLightmapSourceUV(swigCPtr, UVChannel);
  }

  public void SetMaterial(string MaterialPathName, int SlotId) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_SetMaterial(swigCPtr, MaterialPathName, SlotId);
  }

  public string GetMaterial(int SlotId) {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetMaterial(swigCPtr, SlotId));
    return ret;
  }

  public int GetMaterialSlotCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetMaterialSlotCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeMaterialID GetMaterialSlotAt(int Index) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMeshElement_GetMaterialSlotAt(swigCPtr, Index);
    FDatasmithFacadeMaterialID ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeMaterialID(cPtr, false);
    return ret;
  }

}
