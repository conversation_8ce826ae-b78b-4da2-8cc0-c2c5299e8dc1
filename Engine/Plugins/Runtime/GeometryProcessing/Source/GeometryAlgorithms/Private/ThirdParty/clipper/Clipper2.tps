<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name> Clipper2 </Name>
  <Location> Engine\Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\Private\ThirdParty <Location>
  <Function> 2D Polygon clipping, offset, and BooleansWe will use it to add these features to Geometry Script  </Function>
  <Eula> https://github.com/AngusJohnson/Clipper2/blob/main/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>