[/Script/VCamCore.VCamInputSettings]
DefaultInputProfile="VCam Actor Defaults"
InputProfiles=(("VCam Actor Defaults", (MappableKeyOverrides=(("Filmback", None),("NextFilmbackPreset", None),("PreviousFilmbackPreset", None),("ClipPlaneAxis", None),("NextReticle", None),("PreviousReticle", None),("ReticleOpacityAxis", None),("Reticle", None),("NextOverlay", None),("PreviousOverlay", None),("OverlayOpacityAxis", None),("Overlay", None),("CustomAspectRatio", None),("ToggleKillRoll", None),("SetCurrentLocationAxisScaling", None),("SetAllLocationAxisScaling", None),("SelectCurrentLocationAxisScaling", None),("SetLocationStabilization", None),("SetRotationStabilization", None),("ToggleHold", None),("Crane Movement", Gamepad_RightY),("Planar Movement", Gamepad_Left2D),("SetJoystickMovementGain", None),("Pan", Gamepad_RightX),("SetJoystickRotationGain", None),("ToggleFlightMode", None),("FocalLength", None),("NextLens", None),("PreviousLens", None),("Lens", None),("Iris", None),("StopDown", None),("StopUp", None),("FocusDistance", None),("FocusDistanceAxis", None),("FocalLengthAxis", None),("FocusDistanceUp", None),("FocusDistanceDown", None),("FocalLengthUp", None),("FocalLengthDown", None),("ISO", None),("NextISO", None),("PreviousISO", None),("ExposureCompensation", None),("ExposureCompensationReset", None),("CreateBookmark", None),("DeleteCurrentBookmark", None),("NextBookmark", None),("PreviousBookmark", None),("CurrentBookmark", None),("NextFrame", None),("PreviousFrame", None),("SetScrubSpeed", None),("SetSequenceFrameNormalized", None),("TogglePlayPause", None),("ToPlaybackEnd", None),("ToPlaybackStart", None),("StartRecording", None),("StopRecording", None),("ToggleRecording", None),("ZeroToStage", None),("ToggleSensorCorrection", None)))))