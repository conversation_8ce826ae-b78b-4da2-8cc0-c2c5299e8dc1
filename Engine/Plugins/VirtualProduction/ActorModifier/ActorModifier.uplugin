{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Actor Modifier", "Description": "Actual implementation of modifiers for actors based on ActorModifierCore plugin", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "PlatformAllowList": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "ActorModifier", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ActorModifierEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ActorModifierLayout", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ActorModifierCore", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}]}